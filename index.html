<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--=============== FAVICON ===============-->
    <link rel="shortcut icon" href="./assets/img/favicon.png" type="image/x-icon">

    <!--=============== BOXICONS ===============-->
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>

    <!--=============== SWIPER CSS ===============-->
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css">
    
    <!--=============== CSS ===============-->
    <link rel="stylesheet" href="assets/css/styles.css">

    <title> 李晓鹏的响应式个人简历 </title>
</head>

<body>
    <!--=============== HEADER ===============-->
    <header class="header" id="header">
        <nav class="nav container">
            <a href="#" class="nav__logo">安徽美誉制药有限公司</a>
            <!-- 导航菜单 -->
            <div class="nav__menu">
                <ul class="nav__list">
                    <li class="nav__item">
                        <a href="#home" class="nav__link active-link">
                            <i class='bx bx-home-heart'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#about" class="nav__link">
                            <i class='bx bx-user'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#skills " class="nav__link">
                            <i class='bx bx-book-heart'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#work" class="nav__link">
                            <i class='bx bx-briefcase-alt-2'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#contact" class="nav__link">
                            <i class='bx bx-message-square-detail'></i>
                        </a>
                    </li>
                </ul>
            </div>
            <!-- 主题切换按钮 -->
            <i class='bx bx-moon change-theme' id="theme-button"></i>
        </nav>
    </header>

    <!--=============== MAIN ===============-->
    <main class="main">
        <!--=============== HOME ===============-->
        <section class="home section" id="home">
            <div class="home__container container grid">
                <div class="home__carousel">
                    <div class="carousel-container">
                        <div class="carousel-slides">
                            <img src="assets/img/Image1.jpg" alt="轮播图1" class="carousel-img active">
                            <img src="assets/img/Image2.jpg" alt="轮播图2" class="carousel-img">
                            <img src="assets/img/Image3.jpg" alt="轮播图3" class="carousel-img">
                            <img src="assets/img/Image4.jpg" alt="轮播图4" class="carousel-img">
                        </div>
                        <div class="carousel-dots">
                            <span class="dot active" data-slide="0"></span>
                            <span class="dot" data-slide="1"></span>
                            <span class="dot" data-slide="2"></span>
                            <span class="dot" data-slide="3"></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== ABOUT ===============-->
        <section class="about section" id="about">
            <div class="about__container grid">
                <div class="about__expandable">
                    <div class="expandable__item">
                        <div class="expandable__header" data-target="product-info">
                            <h3 class="expandable__title">产品信息</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="product-info">
                            <div class="expandable__body">
                                <div class="product-info-form">
                                    <div class="form-row">
                                        <label class="form-label">品名</label>
                                        <input type="text" class="form-input" value="党参" readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">规格</label>
                                        <input type="text" class="form-input" value="净制" readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">产地</label>
                                        <input type="text" class="form-input" value="广东省肇庆市" readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">执行标准</label>
                                        <input type="text" class="form-input" value="《中国药典》2020年版一部及四部" readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">包装规格</label>
                                        <input type="text" class="form-input" value="1kg" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="production-trace">
                            <h3 class="expandable__title">生产追溯</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="production-trace">
                            <div class="expandable__body">
                                <!-- 生产追溯内容将在这里显示 -->
                                <p>生产追溯详细内容...</p>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="company-intro">
                            <h3 class="expandable__title">企业介绍</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="company-intro">
                            <div class="expandable__body">
                                <!-- 企业介绍内容将在这里显示 -->
                                <p>企业介绍详细内容...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== SKILLS ===============-->
        <section class="skills section" id="skills">
            <span class="section__subtitle">我都会哪些？</span>
            <h2 class="section__title">我掌握的技术</h2>

            <div class="skills__container container grid">

                <div class="skills__content">
                    <h3 class="skills__title">前端开发</h3>

                    <div class="skills__box">
                        <!-- 技能树1 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">HTML</h3>
                                    <span class="skills__level">熟练</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">CSS</h3>
                                    <span class="skills__level">熟练</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">JavaScript</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                        <!-- 技能树2 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Vue2/Vue3</h3>
                                    <span class="skills__level">入门</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">微信小程序</h3>
                                    <span class="skills__level">入门</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">ElementUI</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="skills__content">
                    <h3 class="skills__title">后端开发</h3>

                    <div class="skills__box">
                        <!-- 技能树1 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Java</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Pyhton</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Node Js</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                        <!-- 技能树2 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">MySQL</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">微信小程序云开发</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Firebase</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== SERVICES ===============-->
        <section class="services section">
            <span class="section__subtitle">我能做什么？</span>
            <h2 class="section__title">我可以运用这些技术</h2>

            <div class="services__container container grid">
                <div class="services__card">
                    <h3 class="services__title">网页设计与制作</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">网页设计与制作</h3>
                            <p class="services__modal-description">
                                xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="services__card">
                    <h3 class="services__title">Vue项目开发</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">Vue项目开发</h3>
                            <p class="services__modal-description">
                                xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="services__card">
                    <h3 class="services__title">小程序开发</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">小程序开发</h3>
                            <p class="services__modal-description">
                                我说这是嘛呀xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        一个xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        两个xxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== WORK ===============-->
        <section class="work section" id="work">
            <span class="section__subtitle">我做过哪些？</span>
            <h2 class="section__title">我最近的项目</h2>

            <div class="work__filters">
                <span class="work__item active-work" data-filter="all">全部</span>
                <span class="work__item" data-filter=".web">网页</span>
                <span class="work__item" data-filter=".mini">小程序</span>
                <span class="work__item" data-filter=".admin">后台管理系统</span>
            </div>

            <div class="work__container container grid">
                <div class="work__card mix web">
                    <img src="assets/img/work1.jpg" alt="" class="work__img">
                    <h3 class="work__title">网页</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix mini">
                    <img src="assets/img/work2.jpg" alt="" class="work__img">
                    <h3 class="work__title">小程序</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix mini">
                    <img src="assets/img/work3.jpg" alt="" class="work__img">
                    <h3 class="work__title">小程序</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix web">
                    <img src="assets/img/work4.jpg" alt="" class="work__img">
                    <h3 class="work__title">网页</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix admin">
                    <img src="assets/img/work5.jpg" alt="" class="work__img">
                    <h3 class="work__title">后台管理系统</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
            </div>
        </section>

        <!--=============== TESTIMONIALS ===============-->
        <section class="testimonial section">
            <span class="section__subtitle">认识我的人是这么说的</span>
            <h2 class="section__title">我的评价</h2>

            <div class="testimonial__container container swiper">
                <div class=" swiper-wrapper">
                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial1.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">舍友陈同学</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>

                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial2.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">辅导员张老师</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>

                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial3.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">同事小林</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </section>

        <!--=============== CONTACT ===============-->
        <section class="contact section" id="contact">
            <span class="section__subtitle">如何与我联系？</span>
            <h2 class="section__title">我的联系方式</h2>

            <div class="contact__container container grid">
                <div class="contactt__content">
                    <h3 class="contactt__title">与我交流</h3>

                    <div class="contact__info">
                        <div class="contact__card">
                            <i class='bx bx-phone-call contact__card-icon'></i>
                            <h3 class="contact__card-title">电话</h3>
                            <span class="contact__card-data">123456789</span>

                            <a href="" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                        <div class="contact__card">
                            <i class='bx bx-mail-send contact__card-icon'></i>
                            <h3 class="contact__card-title">邮箱</h3>
                            <span class="contact__card-data"><EMAIL></span>

                            <a href="mailto:<EMAIL>" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                        <div class="contact__card">
                            <i class='bx bxl-whatsapp contact__card-icon'></i>
                            <h3 class="contact__card-title">微信</h3>
                            <span class="contact__card-data">ImPump_</span>

                            <a href="" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                    </div>
                </div>
        </section>
    </main>

    <!--=============== FOOTER ===============-->
    <footer class="footer">
        <div class="footer__container container">
            <h1 class="footer__title">李晓鹏</h1>
            <ul class="footer__list">
                <li>
                    <a href="#about" class="footer__link">我的信息</a>
                </li>
                <li>
                    <a href="#skills" class="footer__link">我的技能</a>
                </li>
                <li>
                    <a href="#work" class="footer__link">我的项目</a>
                </li>
            </ul>

            <ul class="footer__social">
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-facebook'></i>
                </a>
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-instagram'></i>
                </a>
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-twitter' ></i>
                </a>
            </ul>

            <span class="footer__copy">
                &#169;ImPump. All rights reserved
            </span>
        </div>
    </footer>

    <!--=============== SCROLLREVEAL ===============-->
    <script src="assets/js/scrollreveal.min.js"></script>

    <!--=============== SWIPER JS ===============-->
    <script src="assets/js/swiper-bundle.min.js"></script>

    <!--=============== MIXITUP FILTER ===============-->
    <script src="assets/js/mixitup.min.js"></script>

    <!--=============== MAIN JS ===============-->
    <script src="assets/js/main.js"></script>

    <!--=============== API CALL SCRIPT ===============-->
    <script>
        // 调用昆鹏360接口的函数（通过本地代理服务器）
        async function callKunpengAPI() {
            const proxyUrl = 'http://localhost:3000/api';

            try {
                console.log('正在通过代理服务器调用API:', proxyUrl);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('代理服务器响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('=== API调用结果 ===');
                console.log('完整响应:', result);

                if (result.success) {
                    console.log('=== 原始API返回数据 ===');
                    console.log(result.rawData);

                    console.log('=== 解析后的数据 ===');
                    console.log(result.data);

                    console.log('=== API响应状态码 ===');
                    console.log(result.statusCode);

                    console.log('=== API响应头 ===');
                    console.log(result.headers);
                } else {
                    console.error('API调用失败:', result.error);
                }

                return result;

            } catch (error) {
                console.error('调用代理服务器时发生错误:', error);
                console.error('错误详情:', error.message);
                console.log('请确保代理服务器正在运行 (node api-server.js)');
                return null;
            }
        }

        // 页面加载完成后自动调用API
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始调用API...');
            callKunpengAPI();
        });

        // 也可以手动调用
        window.callKunpengAPI = callKunpengAPI;
    </script>
</body>

</html>